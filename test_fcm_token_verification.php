<?php
/**
 * Test FCM Token Verification System
 * 
 * This script tests the new FCM v1 OAuth2 token verification system
 * that checks both cached token validity and database existence.
 */

require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>🔐 FCM Token Verification Test</h1>";

try {
    $db = new Database();
    
    echo "<h2>1. Database Setup Check</h2>";
    
    // Check if fcm_tokens table exists
    $db->query("SHOW TABLES LIKE 'fcm_tokens'");
    $tableExists = $db->single();
    
    if (!$tableExists) {
        echo "<p style='color: red;'>❌ fcm_tokens table does not exist!</p>";
        echo "<p>Run the FCM migration script first.</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ fcm_tokens table exists</p>";
    
    // Get token statistics
    $db->query("SELECT COUNT(*) as total, SUM(active) as active FROM fcm_tokens");
    $stats = $db->single();
    
    echo "<p><strong>Total tokens:</strong> {$stats->total}</p>";
    echo "<p><strong>Active tokens:</strong> {$stats->active}</p>";
    
    echo "<h2>2. API Endpoint Test</h2>";
    
    // Test the new verification endpoint
    $testUrl = (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . '/api/pwa/fcm-verify-token';
    echo "<p><strong>Endpoint:</strong> <code>{$testUrl}</code></p>";
    
    // Check if we can access the endpoint (requires authentication)
    echo "<p style='color: orange;'>⚠️ This endpoint requires user authentication</p>";
    echo "<p>To test manually:</p>";
    echo "<ol>";
    echo "<li>Log in to your application</li>";
    echo "<li>Open browser developer tools</li>";
    echo "<li>Run this JavaScript in the console:</li>";
    echo "</ol>";
    
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
    echo "fetch('/api/pwa/fcm-verify-token', {\n";
    echo "    method: 'POST',\n";
    echo "    headers: {\n";
    echo "        'Content-Type': 'application/json',\n";
    echo "        'X-Requested-With': 'XMLHttpRequest'\n";
    echo "    },\n";
    echo "    body: JSON.stringify({\n";
    echo "        fcm_token: 'your-test-token-here'\n";
    echo "    })\n";
    echo "})\n";
    echo ".then(response => response.json())\n";
    echo ".then(data => console.log('Response:', data))\n";
    echo ".catch(error => console.error('Error:', error));";
    echo "</pre>";
    
    echo "<h2>3. System Architecture</h2>";
    echo "<div style='background: #e8f4fd; padding: 15px; border-radius: 5px; border-left: 4px solid #2196F3;'>";
    echo "<h4>🔄 Enhanced Token Verification Flow</h4>";
    echo "<ol>";
    echo "<li><strong>Time Check:</strong> Verify cached token hasn't expired (7 days)</li>";
    echo "<li><strong>Database Check:</strong> Verify token exists and is active in database</li>";
    echo "<li><strong>Fallback:</strong> If database check fails, use cached token anyway</li>";
    echo "<li><strong>Refresh:</strong> If either check fails, generate new token</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>4. Performance Impact</h2>";
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; border-left: 4px solid #4CAF50;'>";
    echo "<h4>✅ Minimal Performance Impact</h4>";
    echo "<ul>";
    echo "<li>Database check only runs when token exists in cache</li>";
    echo "<li>Single lightweight SQL query per verification</li>";
    echo "<li>Graceful fallback if database check fails</li>";
    echo "<li>Updates last_used timestamp for active tokens</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>5. Benefits</h2>";
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107;'>";
    echo "<h4>🎯 Key Improvements</h4>";
    echo "<ul>";
    echo "<li><strong>Database Consistency:</strong> Ensures cached tokens match database state</li>";
    echo "<li><strong>Automatic Cleanup:</strong> Removes invalid cached tokens immediately</li>";
    echo "<li><strong>Reliability:</strong> Prevents using tokens that were removed from database</li>";
    echo "<li><strong>Security:</strong> Validates token ownership per user session</li>";
    echo "</ul>";
    echo "</div>";
    
    if ($stats->active > 0) {
        echo "<h2>6. Test with Real Token</h2>";
        
        // Get a sample active token for testing
        $db->query("SELECT token, user_id FROM fcm_tokens WHERE active = 1 LIMIT 1");
        $sampleToken = $db->single();
        
        if ($sampleToken) {
            $tokenPreview = substr($sampleToken->token, 0, 30) . '...';
            echo "<p><strong>Sample token found:</strong> {$tokenPreview}</p>";
            echo "<p><strong>User ID:</strong> {$sampleToken->user_id}</p>";
            echo "<p style='color: orange;'>⚠️ You can test with this token when logged in as user {$sampleToken->user_id}</p>";
        }
    }
    
    echo "<h2>7. Next Steps</h2>";
    echo "<p>The FCM token verification system is now enhanced with database validation.</p>";
    echo "<p><strong>What happens now:</strong></p>";
    echo "<ul>";
    echo "<li>Users will automatically get fresh tokens if their cached token is not in the database</li>";
    echo "<li>The system maintains better consistency between cache and database</li>";
    echo "<li>Performance impact is minimal due to efficient implementation</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<p><em>Test completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>
