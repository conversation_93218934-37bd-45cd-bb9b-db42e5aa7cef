<?php
/**
 * Test FCM Token Verification System
 *
 * This script tests the new FCM v1 OAuth2 token verification system
 * that checks both cached token validity and database existence.
 */

require_once 'config/config.php';
require_once 'core/Database.php';

// Start session to check if user is logged in
session_start();

echo "<h1>🔐 FCM Token Verification Test</h1>";

// Check if user is logged in
$isLoggedIn = isset($_SESSION['user_id']);
$userId = $isLoggedIn ? $_SESSION['user_id'] : null;
$userRole = $isLoggedIn ? ($_SESSION['user_role'] ?? 'unknown') : null;

if ($isLoggedIn) {
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; border-left: 4px solid #28a745; margin-bottom: 20px;'>";
    echo "<h4>✅ User Session Active</h4>";
    echo "<p><strong>User ID:</strong> {$userId}</p>";
    echo "<p><strong>Role:</strong> {$userRole}</p>";
    echo "<p>You can test the API endpoint directly from this page!</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; border-left: 4px solid #dc3545; margin-bottom: 20px;'>";
    echo "<h4>❌ No User Session</h4>";
    echo "<p>You need to be logged in to test the API endpoint.</p>";
    echo "<p><strong>Solution:</strong> <a href='/login' target='_blank'>Login in a new tab</a>, then refresh this page.</p>";
    echo "</div>";
}

try {
    $db = new Database();
    
    echo "<h2>1. Database Setup Check</h2>";
    
    // Check if fcm_tokens table exists
    $db->query("SHOW TABLES LIKE 'fcm_tokens'");
    $tableExists = $db->single();
    
    if (!$tableExists) {
        echo "<p style='color: red;'>❌ fcm_tokens table does not exist!</p>";
        echo "<p>Run the FCM migration script first.</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ fcm_tokens table exists</p>";
    
    // Get token statistics
    $db->query("SELECT COUNT(*) as total, SUM(active) as active FROM fcm_tokens");
    $stats = $db->single();
    
    echo "<p><strong>Total tokens:</strong> {$stats->total}</p>";
    echo "<p><strong>Active tokens:</strong> {$stats->active}</p>";
    
    echo "<h2>2. API Endpoint Test</h2>";

    // Test the new verification endpoint
    $testUrl = (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . '/api/pwa/fcm-verify-token';
    echo "<p><strong>Endpoint:</strong> <code>{$testUrl}</code></p>";

    if ($isLoggedIn) {
        echo "<div style='background: #e8f4fd; padding: 15px; border-radius: 5px; border-left: 4px solid #2196F3; margin: 20px 0;'>";
        echo "<h4>🧪 Interactive API Test</h4>";
        echo "<p>Test the endpoint with different token scenarios:</p>";

        // Get a real token for testing - for the CURRENT logged-in user
        $db->query("SELECT token FROM fcm_tokens WHERE user_id = :user_id AND active = 1 LIMIT 1");
        $db->bind(':user_id', $userId);
        $realToken = $db->single();

        if ($realToken) {
            $tokenPreview = substr($realToken->token, 0, 30) . '...';
            echo "<p><strong>Your real token (User {$userId}):</strong> {$tokenPreview}</p>";

            echo "<button onclick='testWithRealToken()' style='background: #28a745; color: white; padding: 8px 16px; border: none; border-radius: 4px; margin: 5px;'>Test Your Real Token</button>";
        } else {
            echo "<p style='color: orange;'><strong>No FCM tokens found for your user (ID: {$userId})</strong></p>";
            echo "<p>You need to enable push notifications first to generate a token.</p>";
            echo "<p><em>Go to your dashboard and enable notifications, then refresh this page.</em></p>";
        }

        echo "<button onclick='testWithFakeToken()' style='background: #dc3545; color: white; padding: 8px 16px; border: none; border-radius: 4px; margin: 5px;'>Test Fake Token</button>";
        echo "<button onclick='testWithInvalidFormat()' style='background: #ffc107; color: black; padding: 8px 16px; border: none; border-radius: 4px; margin: 5px;'>Test Invalid Format</button>";

        echo "<div id='testResults' style='margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px; display: none;'>";
        echo "<h5>Test Results:</h5>";
        echo "<pre id='resultContent'></pre>";
        echo "</div>";
        echo "</div>";

        // Add JavaScript for testing
        echo "<script>";
        echo "const realToken = " . ($realToken ? "'" . $realToken->token . "'" : "null") . ";";
        echo "
        async function testToken(token, testName) {
            document.getElementById('testResults').style.display = 'block';
            document.getElementById('resultContent').textContent = 'Testing ' + testName + '...';

            try {
                const response = await fetch('/api/pwa/fcm-verify-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        fcm_token: token
                    })
                });

                const data = await response.json();
                document.getElementById('resultContent').textContent =
                    testName + ' Result:\\n' +
                    'Status: ' + response.status + '\\n' +
                    'Response: ' + JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('resultContent').textContent =
                    testName + ' Error:\\n' + error.message;
            }
        }

        function testWithRealToken() {
            if (realToken) {
                testToken(realToken, 'Real Token');
            } else {
                alert('No real token available for your user');
            }
        }

        function testWithFakeToken() {
            testToken('fake_token_that_does_not_exist_in_database_12345', 'Fake Token');
        }

        function testWithInvalidFormat() {
            testToken('short', 'Invalid Format');
        }
        ";
        echo "</script>";

    } else {
        echo "<p style='color: orange;'>⚠️ This endpoint requires user authentication</p>";
        echo "<p><strong>To test:</strong> <a href='/login' target='_blank'>Login first</a>, then refresh this page.</p>";
    }

    // Always show manual testing instructions
    echo "<h4>📋 Manual Console Testing</h4>";
    echo "<p>If you prefer to test in the browser console, use this code:</p>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px; font-size: 12px;'>";
    echo "// Test with a real token (replace with actual token)\n";
    echo "fetch('/api/pwa/fcm-verify-token', {\n";
    echo "    method: 'POST',\n";
    echo "    headers: {\n";
    echo "        'Content-Type': 'application/json',\n";
    echo "        'X-Requested-With': 'XMLHttpRequest'\n";
    echo "    },\n";
    echo "    body: JSON.stringify({\n";
    echo "        fcm_token: 'your-actual-fcm-token-here'\n";
    echo "    })\n";
    echo "})\n";
    echo ".then(response => response.json())\n";
    echo ".then(data => console.log('Response:', data))\n";
    echo ".catch(error => console.error('Error:', error));\n\n";
    echo "// Test with fake token\n";
    echo "fetch('/api/pwa/fcm-verify-token', {\n";
    echo "    method: 'POST',\n";
    echo "    headers: {\n";
    echo "        'Content-Type': 'application/json',\n";
    echo "        'X-Requested-With': 'XMLHttpRequest'\n";
    echo "    },\n";
    echo "    body: JSON.stringify({\n";
    echo "        fcm_token: 'fake_token_12345'\n";
    echo "    })\n";
    echo "})\n";
    echo ".then(response => response.json())\n";
    echo ".then(data => console.log('Fake token response:', data));";
    echo "</pre>";
    
    echo "<h2>3. System Architecture</h2>";
    echo "<div style='background: #e8f4fd; padding: 15px; border-radius: 5px; border-left: 4px solid #2196F3;'>";
    echo "<h4>🔄 Enhanced Token Verification Flow</h4>";
    echo "<ol>";
    echo "<li><strong>Time Check:</strong> Verify cached token hasn't expired (7 days)</li>";
    echo "<li><strong>Database Check:</strong> Verify token exists and is active in database</li>";
    echo "<li><strong>Fallback:</strong> If database check fails, use cached token anyway</li>";
    echo "<li><strong>Refresh:</strong> If either check fails, generate new token</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>4. Performance Impact</h2>";
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; border-left: 4px solid #4CAF50;'>";
    echo "<h4>✅ Minimal Performance Impact</h4>";
    echo "<ul>";
    echo "<li>Database check only runs when token exists in cache</li>";
    echo "<li>Single lightweight SQL query per verification</li>";
    echo "<li>Graceful fallback if database check fails</li>";
    echo "<li>Updates last_used timestamp for active tokens</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>5. Benefits</h2>";
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107;'>";
    echo "<h4>🎯 Key Improvements</h4>";
    echo "<ul>";
    echo "<li><strong>Database Consistency:</strong> Ensures cached tokens match database state</li>";
    echo "<li><strong>Automatic Cleanup:</strong> Removes invalid cached tokens immediately</li>";
    echo "<li><strong>Reliability:</strong> Prevents using tokens that were removed from database</li>";
    echo "<li><strong>Security:</strong> Validates token ownership per user session</li>";
    echo "</ul>";
    echo "</div>";
    
    if ($stats->active > 0) {
        echo "<h2>6. Test with Real Token</h2>";
        
        // Get a sample active token for testing
        $db->query("SELECT token, user_id FROM fcm_tokens WHERE active = 1 LIMIT 1");
        $sampleToken = $db->single();
        
        if ($sampleToken) {
            $tokenPreview = substr($sampleToken->token, 0, 30) . '...';
            echo "<p><strong>Sample token found:</strong> {$tokenPreview}</p>";
            echo "<p><strong>User ID:</strong> {$sampleToken->user_id}</p>";
            echo "<p style='color: orange;'>⚠️ You can test with this token when logged in as user {$sampleToken->user_id}</p>";
        }
    }
    
    echo "<h2>7. Next Steps</h2>";
    echo "<p>The FCM token verification system is now enhanced with database validation.</p>";
    echo "<p><strong>What happens now:</strong></p>";
    echo "<ul>";
    echo "<li>Users will automatically get fresh tokens if their cached token is not in the database</li>";
    echo "<li>The system maintains better consistency between cache and database</li>";
    echo "<li>Performance impact is minimal due to efficient implementation</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<p><em>Test completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>
